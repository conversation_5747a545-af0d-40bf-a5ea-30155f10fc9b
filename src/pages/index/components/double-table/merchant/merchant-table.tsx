import {
  MERCHANT_BUTTON_CODE_ENUM,
  OUTBOUND_DETAIL_PANEL_TAB_ENUM,
  TASK_STATUS,
} from '@/common/const';
import { IfButtonShow, useAction } from '@/components/server-controller/useAction';
import { isAmapXy } from '@/utils';
import {
  TableColumnType,
  Flex,
  Tooltip,
  Space,
  Table,
  message,
  Checkbox,
  Tag,
  Form,
  PaginationProps,
  TableProps,
  Button,
} from 'antd';
import Link from '@/components/Link';
import styled from 'styled-components';
import { ModalTriggerWrapper } from '../../modal-trigger-wrapper';
import { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { AgentOperationMerchantRelationDTO } from '@/_docplus/target/types/amap-sales-operation-client';
import { sendEvent, getMerchantLabels } from '@/common/utils';
import OrderModal from '@/components/order-modal';
import useModal from '@/hooks/useModal';
import { WeeklyReportDrawer } from '@/pages/business-news/components/weekly-report-drawer';
import { openParentPage, getEnv } from '@alife/kb-biz-util';
import { AlertTaskDrawer } from '../../alert-task-drawer';
import { OperationServiceDetail } from '../../operation-service-detail';
import { OutBoundDetailDrawer } from '../../outbound-detail';
import TaskDetailDrawer from '../../task-detail-drawer';
import { ActionButtonType } from '@/constants';
import { IconFontQiWei } from '@/components/icons';
import copy from 'copy-to-clipboard';
import BatchReport from '../../BatchReport';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import TaskCountdown from '../../todo-list/TaskCountdown';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { useRequest } from 'ahooks';
import { createOptGroup } from '@/services';

const StyledTaskDescription = styled.div`
  display: -webkit-inline-box;
  padding: 2px 14px;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  border-radius: 20px;
  opacity: 1;
  background: linear-gradient(
    90deg,
    rgba(55, 165, 255, 0.1) 0%,
    rgba(0, 209, 209, 0.1) 48%,
    rgba(85, 222, 140, 0.1) 100%
  );
`;

const StyledCheckbox = styled.div`
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  font-weight: 500;
`;

interface MerchantTableRef {
  openAlertTaskDrawer: (data: any) => void;
}

interface IProps {
  list: any[];
  onSearch: () => void;
  pagination: PaginationProps;
  loading: boolean;
  onChange: TableProps['onChange'];
  onSwithShopList: (params: any) => void;
}

const MerchantTable = forwardRef<MerchantTableRef, IProps>((props, ref) => {
  const {
    list = [],
    onSearch,
    pagination,
    loading = false,
    onChange: tableOnChange,
    onSwithShopList,
  } = props;
  const [outBoundDetailVisible, setOutBoundDetailVisible] = useState(false);
  const [outBoundDetailData, setOutBoundDetailData] = useState<any>({});
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [merchantData, setMerchantData] = useState<any>({});
  const [operationServiceVisible, setOperationServiceVisible] = useState(false);
  const [operationServiceData, setOperationServiceData] = useState<any>({});

  const multipleMerchant = useAction(ActionButtonType.商户多选);
  const canSelect = multipleMerchant.showButton;

  const [selectedPidList, setSelectedPidList] = useState<React.Key[]>([]);
  const { updateAndOpen, modalProps } = useModal<{ pid: string }>();
  const { updateAndOpen: openAlertTaskDrawer, modalProps: alertTaskDrawerProps } = useModal<{
    pid: string;
    defaultTab?: string;
  }>();
  const { updateAndOpen: openTaskDetailDrawer, modalProps: taskDetailDrawerProps } = useModal();
  // 去完成和查看任务的文案，需依赖taskStatus，其他按钮直接用接口返回name
  const rendeActionButtonName = (actionItem: any, record: any) => {
    const completed = record?.taskStatus === TASK_STATUS.COMPLETED;
    if (actionItem?.buttonCode === MERCHANT_BUTTON_CODE_ENUM.TO_COMPLETE) {
      return completed ? '查看任务' : '去完成';
    }
    return actionItem?.buttonName;
  };
  const handleSwitchShopList = (merchant: AgentOperationMerchantRelationDTO) => {
    onSwithShopList({ pid: merchant.pid, ppid: merchant.pid });
  };
  // const openTaskDetailDrawer = (merchant: AgentOperationMerchantRelationDTO) => {
  //   setTaskDetailData({
  //     pid: merchant.pid,
  //     jumpSource: TASK_DETAIL_JUMP_SOURCE.MERCHANT_LIST,
  //     from: TASK_DETAIL_JUMP_FROM.MERCHANT_LIST,
  //   });
  //   setTaskDetailDrawerVisible(true);
  // };
  const openDrawer = (pid: string, merchantName: string, hasOptGroup: boolean) => {
    setMerchantData({
      pid,
      merchantName,
      hasOptGroup,
    });
    setDrawerVisible(true);
  };
  const openVisitRecord = (record: any) => {
    if (record.showCallButton) {
      setOutBoundDetailData({
        pid: record.pid,
        merchantName: record.merchantName,
        visitVisible: true,
        boundDetailTabKey: OUTBOUND_DETAIL_PANEL_TAB_ENUM.VISIT_RECORD,
      });
      setOutBoundDetailVisible(true);
    } else if (isAmapXy()) {
      window.open(
        `${location.origin}/sale-pc/kb-visit-pc/pc/create-visit-record?hideMenu=true&targetId=${record.pid}&targetType=MERCHANT`,
      );
    } else {
      const hostnameMap = {
        DAILY: '//r.elenet.me',
        PRE: '//ppe-r.ele.me',
        PROD: '//r.ele.me',
      };
      openParentPage({
        origin: hostnameMap[getEnv()],
        project: 'kb-visit-pc/pc',
        url: `create-visit-record?targetId=${record.pid}&targetType=MERCHANT`,
      });
    }
  };
  const openOutBoundDetailDrawer = (record: any) => {
    setOutBoundDetailData({
      pid: record.pid,
      merchantName: record.merchantName,
    });
    setOutBoundDetailVisible(true);
  };

  const handleOutBoundDetailDrawerClose = () => {
    setOutBoundDetailVisible(false);
  };

  const openOperationServiceDrawer = (record: any) => {
    setOperationServiceData({
      pid: record.pid,
      merchantName: record.merchantName,
    });
    setOperationServiceVisible(true);
  };

  const handleOperationServiceDrawerClose = () => {
    setOperationServiceVisible(false);
  };

  const handleClose = (visible) => {
    setDrawerVisible(visible);
    message.destroy();
  };

  const handleActionClick = (record: any, buttonCode: string, buttonName: string) => {
    const { taskStatus, pid, merchantName, optGroupName: hasOptGroup } = record;
    const completed = taskStatus === TASK_STATUS.COMPLETED;

    traceClick(PageSPMKey.首页, ModuleSPMKey['商户列表.操作项'], {
      operation: buttonName,
      pid: record.pid,
    });

    switch (buttonCode) {
      case MERCHANT_BUTTON_CODE_ENUM.RECHARGE_EXTENSION:
        updateAndOpen({ pid: record.pid });
        sendEvent('RECHARGE_EXTENSION', 'CLK');
        break;
      case MERCHANT_BUTTON_CODE_ENUM.VIEW_STORES:
        handleSwitchShopList(record);
        sendEvent('VIEW_SHOP_LIST_BTN', 'CLK');
        break;
      case MERCHANT_BUTTON_CODE_ENUM.TO_COMPLETE:
        openTaskDetailDrawer(record);
        sendEvent(completed ? 'VIEW_TASK_DETAIL_BTN' : 'TO_DO_TASK_BTN', 'CLK');
        if (record.priorityTaskInfo?.taskNo) {
          traceClick(PageSPMKey.首页, ModuleSPMKey['商户详情.去完成任务按钮点击'], {
            pid: record.pid,
            taskNo: record.priorityTaskInfo.taskNo,
          });
        }
        break;
      case MERCHANT_BUTTON_CODE_ENUM.GENERATE_NEWS:
        openDrawer(pid, merchantName, !!hasOptGroup);
        sendEvent('CREATE_BUSINESS_NEWS_BTN', 'CLK');
        break;
      case MERCHANT_BUTTON_CODE_ENUM.RECORD_VISITS:
        openVisitRecord(record);
        break;
      case MERCHANT_BUTTON_CODE_ENUM.TELEPHONE_COMMUNICATION:
        openOutBoundDetailDrawer(record);
        break;
      case MERCHANT_BUTTON_CODE_ENUM.OPERATE_SERVICES:
        openOperationServiceDrawer(record);
        break;
      // 预警处理
      case MERCHANT_BUTTON_CODE_ENUM.WARNING_HANDLE:
        openAlertTaskDrawer({
          pid: record.pid,
        });
        break;
      // 一键建群
      case MERCHANT_BUTTON_CODE_ENUM.CREATE_OPT_GROUP:
        handleCreateGroup(record);
        break;
      default:
        break;
    }
  };

  // 一键建群处理函数
  const { run: handleCreateGroup } = useRequest(
    async (record: any) => {
      try {
        const { pid } = record;
        const res = await createOptGroup({
          pid,
          bizSource: 'operate',
        });

        if (res) {
          const messageText = res;

          if (messageText.includes('复制')) {
            // 提取群名
            const groupNameMatch = messageText.match(/群名[:：]?[「'""]([^」'""]+)[」'""]/);
            const groupName = groupNameMatch?.[1] || '';

            // 显示可复制的message
            message.success({
              content: (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <span>{messageText.split('，点击复制群名')[0]}</span>
                  {groupName && (
                    <Button
                      type="link"
                      size="small"
                      style={{ padding: 0, height: 'auto', color: '#1890ff' }}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (copy(groupName)) {
                          message.success('复制群名成功');
                        }
                      }}
                    >
                      点击复制群名
                    </Button>
                  )}
                </div>
              ),
              duration: 6,
            });
          } else {
            message.success(messageText);
          }
          props.onSearch();
        }
      } catch (error) {
        console.error('建群失败:', error);
      }
    },
    {
      manual: true,
    },
  );
  const columns: TableColumnType[] = [
    {
      title: '商户信息',
      dataIndex: 'merchantName',
      key: 'merchantName',
      width: 210,
      align: 'center',

      render: (value, record) => {
        return (
          <>
            <div>
              <span>商户名称：{value || '-'}</span>
              {record.optGroupName && (
                <IconFontQiWei
                  onClick={() => {
                    const res = copy(record.optGroupName);
                    if (res) {
                      message.success('复制群名成功');
                    }
                  }}
                  style={{ cursor: 'pointer', marginLeft: 6 }}
                />
              )}
            </div>
            <div style={{ color: 'rgba(0, 0, 0, 0.65)' }}>PID：{record.pid || '-'}</div>
            <div>主店名：{record.mainShopName || '-'}</div>
            {record.merchantLabels &&
              record.merchantLabels.length > 0 &&
              record.merchantLabels.map((item: any) => (
                <Tag
                  className="merchant-labels-tag"
                  color={getMerchantLabels(item?.code).color}
                  key={item}
                >
                  {item?.name || '-'}
                </Tag>
              ))}
          </>
        );
      },
    },
    {
      title: (
        <Flex gap={4} align="center" justify="center">
          <img
            style={{ width: 20, height: 20 }}
            src="https://img.alicdn.com/imgextra/i4/O1CN01HT4TB51FdAdgpz0NN_!!6000000000509-2-tps-72-72.png"
          />
          商户诊断
        </Flex>
      ),
      dataIndex: 'taskDesc',
      align: 'center',
      width: 200,
      render: (text, record) => (
        <Tooltip title={record.priorityTaskInfo?.taskDesc}>
          <StyledCheckbox>{record.priorityTaskInfo?.taskDesc || '-'}</StyledCheckbox>
        </Tooltip>
      ),
    },
    {
      title: '高优任务',
      dataIndex: 'taskName',
      align: 'center',
      render: (text, record) => {
        const taskExpireTime = record.priorityTaskInfo?.manageLimit;
        const taskName = record.priorityTaskInfo?.taskName || '';
        if (!taskExpireTime && !taskName) {
          return '-';
        }
        return (
          <>
            <div style={{ color: '#1A66FF', fontWeight: 500, fontSize: 16 }}>
              {record.priorityTaskInfo.taskName}
            </div>
            <TaskCountdown
              style={{ background: 'rgba(0, 0, 0, 0.04)', width: 150, display: 'inline-block' }}
              expireTime={taskExpireTime}
              overtimeStyle={{ display: 'inline-block', width: 'auto' }}
            />
          </>
        );
      },
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatusText',
      render: (text, record) => {
        if (!record.priorityTaskInfo?.taskStatusText) {
          return '-';
        }
        return (
          <div
            style={{
              fontWeight: 500,
              fontSize: 16,
              color: '#F5222D',
            }}
          >
            {record.priorityTaskInfo?.taskStatusText || '-'}
          </div>
        );
      },
      align: 'center',
    },
    {
      title: '商户推荐分析',
      dataIndex: 'recommendAnalysis',
      align: 'center',
      width: 270,
      render: (text, record) => {
        const isHighPotential = record?.highPotentialValues?.[0] === '1';
        if (!record.priorityTaskInfo?.recommendAnalysis && !isHighPotential) {
          return '-';
        }
        return (
          <Space style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            {isHighPotential && <StyledTaskDescription>潜力广告主</StyledTaskDescription>}

            <Tooltip title={record.priorityTaskInfo?.recommendAnalysis}>
              <StyledTaskDescription>
                {record.priorityTaskInfo?.recommendAnalysis || '-'}
              </StyledTaskDescription>
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '商户标签',
      dataIndex: 'merchantLevel',
      key: 'merchantLevel',
      width: 150,
      align: 'center',
      render: (value) => {
        return <span>{value?.name || '-'}</span>;
      },
    },
    {
      title: '服务满意度',
      dataIndex: 'serviceSatisfaction',
      key: 'serviceSatisfaction',
      width: 150,
      align: 'center',
      render: (value) => {
        return (
          <span>
            {value?.score ? `${value?.score}分` : ''}
            {value?.name ? `（${value?.name}）` : '-'}
          </span>
        );
      },
    },
    {
      title: '当月广告总消耗',
      dataIndex: 'adCurrentMonthCost',
      key: 'adCurrentMonthCost',
      width: 150,
      sorter: true,
      align: 'center',
      render: (value) => {
        return <span>{value || '-'}</span>;
      },
    },
    {
      title: '广告现金余额',
      dataIndex: 'adCurrentBalance',
      key: 'adCurrentBalance',
      width: 137,
      sorter: true,
      align: 'center',
      render: (value, record) => {
        return <span>{record.adCurrentBalance || '-'}</span>;
      },
    },
    {
      title: '累计充值次数',
      dataIndex: 'adRechargeCount',
      key: 'adRechargeCount',
      width: 150,
      align: 'center',
      render: (value) => {
        return <span>{value || '-'}</span>;
      },
    },
    {
      title: '运维人员',
      key: 'staffName',
      width: 150,
      render: (value) => {
        return (
          <div>
            <div>运维关系：{value?.staffName || '-'}</div>
            <div>基建运维：{value?.infrastructStaffName || '-'}</div>
            <div>新签关系：{value?.newSignName || '-'}</div>
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      align: 'center',
      fixed: 'right',
      render: (value, record) => {
        return (
          <Space wrap>
            {record?.actions?.map((item) => (
              <IfButtonShow
                button={item}
                // ext={item.buttonCode === MERCHANT_BUTTON_CODE_ENUM.RECHARGE_EXTENSION}
              >
                <ModalTriggerWrapper button={item} record={record}>
                  <Link
                    disabled={item.greyButton}
                    to=""
                    onClick={(e) => {
                      e.stopPropagation();
                      handleActionClick(record, item?.buttonCode, item?.buttonName);
                    }}
                  >
                    {rendeActionButtonName(item, record)}
                  </Link>
                </ModalTriggerWrapper>
              </IfButtonShow>
            ))}
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    const openAlert = (data: any) => {
      if (data.openDrawer === 'warn') {
        openAlertTaskDrawer({ pid: data.pid, defaultTab: data.tab });
      }
    };
    const afterBatchReport = () => {
      setSelectedPidList([]);
    };
    const clearSelection = () => {
      setSelectedPidList([]);
    };
    emitter.on(EmitterEventMap.OpenDrawer, openAlert);
    emitter.on(EmitterEventMap.AfterBatchReport, afterBatchReport);
    emitter.on(EmitterEventMap.ClearSelection, clearSelection);
    return () => {
      emitter.off(EmitterEventMap.OpenDrawer, openAlert);
      emitter.off(EmitterEventMap.AfterBatchReport, afterBatchReport);
      emitter.off(EmitterEventMap.ClearSelection, clearSelection);
    };
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      openAlertTaskDrawer: (data: any) => {
        openAlertTaskDrawer({ pid: data.pid, defaultTab: data.tab });
      },
    }),
    [openAlertTaskDrawer],
  );

  const rowSelection = canSelect
    ? {
        preserveSelectedRowKeys: true,
        selectedRowKeys: selectedPidList.map((item) => item.id),
        onChange: (selectedRowKeys: string[], selectedRows) => {
          if (selectedRows && selectedRows.length <= 100) {
            setSelectedPidList(
              selectedRows
                .filter((item) => !!item)
                .map((item) => ({
                  name: item.merchantName,
                  id: item.pid,
                })),
            );
          } else {
            message.warning('最多选择100个商户');
          }
        },
        getCheckboxProps(record) {
          return {
            disabled: !record.optGroupCanReach,
          };
        },
      }
    : undefined;
  return (
    <div style={{ marginTop: 12 }}>
      <Flex justify="space-between" align="center" style={{ marginBottom: 12 }}>
        <Space>
          <IfButtonShow buttonType={ActionButtonType.仅查看有群商户}>
            {(button) => (
              <Form.Item
                name="hasOptGroup"
                valuePropName="checked"
                wrapperCol={{ style: { width: 140 } }}
                colon={false}
                style={{ marginBottom: 0 }}
                initialValue={false}
              >
                <Checkbox disabled={button.greyButton} onChange={() => onSearch()}>
                  {button.buttonText || '仅查看有群商户'}
                </Checkbox>
              </Form.Item>
            )}
          </IfButtonShow>
          <Form.Item
            name="filterOptRelation"
            valuePropName="checked"
            wrapperCol={{ style: { width: 100 } }}
            colon={false}
            style={{ marginBottom: 0 }}
            initialValue={false}
          >
            <Checkbox onChange={() => onSearch()}>只看我的</Checkbox>
          </Form.Item>
        </Space>
        <BatchReport pidList={selectedPidList} />
      </Flex>
      <Table
        rowKey="pid"
        rowSelection={rowSelection}
        columns={columns}
        dataSource={list}
        loading={loading}
        pagination={pagination}
        onChange={tableOnChange}
        scroll={{
          x: 'max-content',
        }}
      />
      {drawerVisible && <WeeklyReportDrawer visible onClose={handleClose} {...merchantData} />}
      {/* {taskDetailDrawerVisible && (
        <TaskDetailDrawer
          visible={taskDetailDrawerVisible}
          onClose={handleTaskDetailDrawerClose}
          {...taskDetailData}
        />
      )} */}
      {outBoundDetailVisible && (
        <OutBoundDetailDrawer
          visible={outBoundDetailVisible}
          onClose={handleOutBoundDetailDrawerClose}
          {...outBoundDetailData}
        />
      )}
      {operationServiceVisible && (
        <OperationServiceDetail
          visible={operationServiceVisible}
          onClose={handleOperationServiceDrawerClose}
          {...operationServiceData}
        />
      )}
      <AlertTaskDrawer {...alertTaskDrawerProps} />
      <OrderModal {...modalProps} />
      <TaskDetailDrawer {...taskDetailDrawerProps} />
    </div>
  );
});

MerchantTable.displayName = 'MerchantTable';

export default MerchantTable;
