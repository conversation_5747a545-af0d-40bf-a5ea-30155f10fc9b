import React, { useState } from 'react';
import { Drawer, But<PERSON>, Table, Pagination, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const DrawerContent = styled.div`
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
  }
`;

const ActionSection = styled.div`
  margin-bottom: 16px;
`;

interface IBatchTaskData {
  key: string;
  distributeTime: string;
  taskType: string;
  taskName: string;
  status: string;
  successCount: string;
  downloadDetails: string;
}

interface IBatchTaskDrawerProps {
  visible: boolean;
  onClose: () => void;
  onDistributeTask: () => void;
}

const BatchTaskDrawer: React.FC<IBatchTaskDrawerProps> = ({
  visible,
  onClose,
  onDistributeTask,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 模拟数据
  const batchTaskData: IBatchTaskData[] = [
    {
      key: '1',
      distributeTime: '',
      taskType: '',
      taskName: '',
      status: '处理中',
      successCount: '39/100',
      downloadDetails: '下载明细',
    },
    {
      key: '2',
      distributeTime: '',
      taskType: '',
      taskName: '',
      status: '下发成功',
      successCount: '99/99',
      downloadDetails: '',
    },
  ];

  const columns = [
    {
      title: '下发时间',
      dataIndex: 'distributeTime',
      key: 'distributeTime',
      width: 150,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      align: 'center' as const,
      width: 150,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      align: 'center' as const,
      width: 200,
    },
    {
      title: '下发状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center' as const,
      render: (status: string) => (
        <span style={{ color: status === '下发成功' ? '#52c41a' : '#1890ff' }}>{status}</span>
      ),
    },
    {
      title: '下发成功数',
      dataIndex: 'successCount',
      key: 'successCount',
      width: 120,
    },
    {
      title: '明细下载',
      dataIndex: 'downloadDetails',
      key: 'downloadDetails',
      width: 120,
      render: (text: string) => (text ? <a style={{ color: '#1890ff' }}>{text}</a> : null),
    },
  ];

  const handleDistributeTask = () => {
    onDistributeTask();
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  };

  return (
    <>
      <Drawer
        title="任务下发"
        placement="right"
        width={800}
        open={visible}
        onClose={onClose}
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleDistributeTask}>
            下发任务
          </Button>
        }
      >
        <DrawerContent>
          <ActionSection>
            <Space>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleDistributeTask}>
                下发任务
              </Button>
            </Space>
          </ActionSection>

          <Table
            columns={columns}
            dataSource={batchTaskData}
            pagination={false}
            scroll={{ x: 800 }}
          />

          <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              current={currentPage}
              total={20}
              pageSize={pageSize}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) => `${range[0]}-${range[1]} 共 ${total} 条`}
              onChange={handlePageChange}
              onShowSizeChange={handlePageChange}
            />
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default BatchTaskDrawer;
