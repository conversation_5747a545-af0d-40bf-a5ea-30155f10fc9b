import React, { useState } from 'react';
import { Modal, Form, Input, Radio, InputNumber, Button, Space, message, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { TextArea } = Input;

const FormContainer = styled.div`
  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-form-item-label > label {
    font-weight: 500;
  }
`;

const RadioGroup = styled(Radio.Group)`
  .ant-radio-wrapper {
    margin-right: 24px;
  }
`;

const UploadSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const TimeInputContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

interface ITaskDistributeFormProps {
  visible: boolean;
  onClose: () => void;
}

const TaskDistributeForm: React.FC<ITaskDistributeFormProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));
      message.success('任务下发成功');
      form.resetFields();
      onClose();
    } catch (error) {
      message.error('任务下发失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  const uploadProps = {
    name: 'file',
    multiple: true,
    action: '/api/upload',
    onChange(info: any) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 文件上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件上传失败`);
      }
    },
  };

  return (
    <Modal
      title="任务下发"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={500}
      destroyOnClose
    >
      <FormContainer>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            taskType: 'type1',
            workingDays: 5,
          }}
        >
          <Form.Item
            label="任务类型"
            name="taskType"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <RadioGroup>
              <Radio value="type1">xx类型</Radio>
              <Radio value="type2">xx类型</Radio>
            </RadioGroup>
          </Form.Item>

          <Form.Item
            label="任务名称"
            name="taskName"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder="请输入名称" />
          </Form.Item>

          <Form.Item
            label="任务描述"
            name="description"
            rules={[{ required: true, message: '请输入任务描述' }]}
          >
            <TextArea rows={4} placeholder="请输入任务描述" />
          </Form.Item>

          <Form.Item
            label="时效要求"
            name="workingDays"
            rules={[{ required: true, message: '请输入时效要求' }]}
          >
            <TimeInputContainer>
              <InputNumber min={1} max={30} style={{ width: 80 }} placeholder="5" />
              <span>工作日</span>
            </TimeInputContainer>
          </Form.Item>

          <Form.Item label="上传文档">
            <UploadSection>
              <span>上传文档</span>
              <Upload {...uploadProps}>
                <Button type="link" icon={<UploadOutlined />}>
                  上传
                </Button>
              </Upload>
            </UploadSection>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={handleCancel}>取消</Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </FormContainer>
    </Modal>
  );
};

export default TaskDistributeForm;
