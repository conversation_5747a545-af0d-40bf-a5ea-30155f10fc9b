import React, { useState } from 'react';
import { Button, Table, Select, Input, Pagination } from 'antd';
import styled from 'styled-components';
import BatchTaskDrawer from './components/batch-task-drawer';
import TaskDistributeForm from './components/task-distribute-form';

const { Option } = Select;

const PageContainer = styled.div`
  padding: 24px;
  background: #fff;
  min-height: 100vh;
`;

const HeaderSection = styled.div`
  margin-bottom: 24px;
`;

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 16px;
`;

const TabItem = styled.div<{ active?: boolean }>`
  padding: 8px 16px;
  margin-right: 8px;
  cursor: pointer;
  border-radius: 4px;
  color: ${(props) => (props.active ? '#1890ff' : '#666')};
  background: ${(props) => (props.active ? '#e6f7ff' : 'transparent')};
  border: 1px solid ${(props) => (props.active ? '#1890ff' : '#d9d9d9')};
  transition: all 0.3s;

  &:hover {
    color: #1890ff;
    border-color: #1890ff;
  }
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
`;

const FilterItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const BatchButton = styled(Button)`
  margin-left: auto;
`;

const TableContainer = styled.div`
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
  }
`;

const PaginationContainer = styled.div`
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
`;

interface ITaskSummaryData {
  key: string;
  team: string;
  avgDays: string;
  reinvestDuration: string;
  hitRate: string;
  arpu: string;
  budget: string;
  children?: ITaskSummaryData[];
}

const TaskSummary: React.FC = () => {
  const [activeTab, setActiveTab] = useState('summary');
  const [batchDrawerVisible, setBatchDrawerVisible] = useState(false);
  const [distributeFormVisible, setDistributeFormVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 模拟数据 - 树形结构
  const summaryData: ITaskSummaryData[] = [
    {
      key: '1',
      team: '电销团队',
      avgDays: '内容名称',
      reinvestDuration: '内容名称',
      hitRate: '内容名称',
      arpu: '内容名称',
      budget: '内容名称',
      children: [
        {
          key: '1-1',
          team: '电销—美食',
          avgDays: '内容名称',
          reinvestDuration: '内容名称',
          hitRate: '内容名称',
          arpu: '内容名称',
          budget: '内容名称',
        },
        {
          key: '1-2',
          team: '电销—休娱',
          avgDays: '内容名称',
          reinvestDuration: '内容名称',
          hitRate: '内容名称',
          arpu: '内容名称',
          budget: '内容名称',
        },
        {
          key: '1-3',
          team: '电销—教培',
          avgDays: '内容名称',
          reinvestDuration: '内容名称',
          hitRate: '内容名称',
          arpu: '内容名称',
          budget: '内容名称',
        },
        {
          key: '1-4',
          team: '电销-CPS',
          avgDays: '内容名称',
          reinvestDuration: '内容名称',
          hitRate: '内容名称',
          arpu: '内容名称',
          budget: '内容名称',
        },
      ],
    },
    {
      key: '2',
      team: '美食&休娱—广告团队',
      avgDays: '内容名称',
      reinvestDuration: '内容名称',
      hitRate: '内容名称',
      arpu: '内容名称',
      budget: '内容名称',
    },
    {
      key: '3',
      team: '美食&休娱-CPS团队',
      avgDays: '内容名称',
      reinvestDuration: '内容名称',
      hitRate: '内容名称',
      arpu: '内容名称',
      budget: '内容名称',
    },
    {
      key: '4',
      team: '教培美业中长尾商家',
      avgDays: '内容名称',
      reinvestDuration: '内容名称',
      hitRate: '内容名称',
      arpu: '内容名称',
      budget: '内容名称',
    },
    {
      key: '5',
      team: '医疗中长尾商家',
      avgDays: '内容名称',
      reinvestDuration: '内容名称',
      hitRate: '内容名称',
      arpu: '内容名称',
      budget: '内容名称',
    },
    {
      key: '6',
      team: '文旅中长尾商家',
      avgDays: '内容名称',
      reinvestDuration: '内容名称',
      hitRate: '内容名称',
      arpu: '内容名称',
      budget: '内容名称',
    },
  ];

  const handleBatchTask = () => {
    setBatchDrawerVisible(true);
  };

  const handleBatchDrawerClose = () => {
    setBatchDrawerVisible(false);
  };

  const handleDistributeTask = () => {
    setDistributeFormVisible(true);
  };

  const handleDistributeFormClose = () => {
    setDistributeFormVisible(false);
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  };

  const summaryColumns = [
    {
      title: '团队',
      dataIndex: 'team',
      key: 'team',
      width: 200,
    },
    {
      title: '店均在投天数',
      dataIndex: 'avgDays',
      key: 'avgDays',
      width: 150,
    },
    {
      title: '再投时长',
      dataIndex: 'reinvestDuration',
      key: 'reinvestDuration',
      width: 150,
    },
    {
      title: '撞线率',
      dataIndex: 'hitRate',
      key: 'hitRate',
      width: 150,
    },
    {
      title: 'ARPU',
      dataIndex: 'arpu',
      key: 'arpu',
      width: 150,
    },
    {
      title: '预算',
      dataIndex: 'budget',
      key: 'budget',
      width: 150,
    },
  ];

  return (
    <PageContainer>
      <HeaderSection>
        <TabContainer>
          <TabItem active={activeTab === 'summary'} onClick={() => setActiveTab('summary')}>
            任务汇总
          </TabItem>
          <TabItem active={activeTab === 'detail'} onClick={() => setActiveTab('detail')}>
            任务明细
          </TabItem>
        </TabContainer>

        <FilterContainer>
          <FilterItem>
            <span>按团队筛选</span>
            <Select placeholder="请选择团队" style={{ width: 150 }}>
              <Option value="team1">电销团队</Option>
              <Option value="team2">美食&休娱团队</Option>
            </Select>
          </FilterItem>
          <FilterItem>
            <span>按部门筛选</span>
            <Select placeholder="请选择部门" style={{ width: 150 }}>
              <Option value="dept1">部门1</Option>
              <Option value="dept2">部门2</Option>
            </Select>
          </FilterItem>
          <FilterItem>
            <span>小二名称</span>
            <Input placeholder="请输入小二名称" style={{ width: 150 }} />
          </FilterItem>
          <BatchButton type="primary" onClick={handleBatchTask}>
            批量下发任务
          </BatchButton>
        </FilterContainer>
      </HeaderSection>

      <TableContainer>
        <Table
          columns={summaryColumns}
          dataSource={summaryData}
          pagination={false}
          scroll={{ x: 1000 }}
          defaultExpandAllRows={false}
          expandable={{
            defaultExpandAllRows: false,
            expandRowByClick: false,
          }}
        />
      </TableContainer>

      <PaginationContainer>
        <Pagination
          current={currentPage}
          total={50}
          pageSize={pageSize}
          showSizeChanger
          showQuickJumper
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </PaginationContainer>

      <BatchTaskDrawer
        visible={batchDrawerVisible}
        onClose={handleBatchDrawerClose}
        onDistributeTask={handleDistributeTask}
      />

      <TaskDistributeForm visible={distributeFormVisible} onClose={handleDistributeFormClose} />
    </PageContainer>
  );
};

export default TaskSummary;
