import { createEspOrder, queryLocalOptEspWoosOrder } from '@/services';
import { useRequest } from 'ahooks';
import { Modal, message } from 'antd';
import { FormInstance } from 'antd/es/form';
import React from 'react';
import { SubmitPermissionResult } from '../../utils';

interface IProps {
  shopId: string;
  form: FormInstance;
  submitPermission: SubmitPermissionResult;
  isRejection: boolean;
  setExpanded: (expanded: boolean) => void;
  refreshAuditData: (auditId?: string) => void;
  auditStatus?: string;
}

// 质检结果类型
interface QualityCheckResult {
  merchantScore: {
    passed: boolean;
    version: string; // 支持所有版本号，如 '1.0', '3.0',
    score?: number;
    level?: string;
  };
  productShelf: {
    passed: boolean;
    hasResult: boolean;
    reason?: string;
  };
  orderAuditType?: any;
  shopScoreInspectInfo?: string; // 后端下发的质检信息JSON字符串
}

export function useSubmit(props: IProps) {
  const { shopId, form, submitPermission, isRejection, setExpanded, refreshAuditData } = props;

  // 获取质检结果弹窗文案
  const getQualityCheckModal = (
    checkResult: QualityCheckResult,
  ): { title: string; content: React.ReactNode; isSuccessMessage?: boolean } | null => {
    const { merchantScore, productShelf, orderAuditType, shopScoreInspectInfo } = checkResult;

    // 如果没有 orderAuditType，说明服务端没有返回不通过原因，不弹弹窗
    if (!orderAuditType) {
      return null;
    }

    // 优先使用后端下发的shopScoreInspectInfo数据
    if (shopScoreInspectInfo) {
      try {
        const inspectData = JSON.parse(shopScoreInspectInfo);
        const { title, reasonInfos } = inspectData;

        if (title && reasonInfos && reasonInfos.length > 0) {
          return {
            title: '提示',
            content: (
              <div>
                <div>
                  {title}
                  <div style={{ height: '8px' }} />
                </div>
                {reasonInfos.map((item: any, index: number) => (
                  <div key={index}>
                    <div>{item.reason}</div>
                    <div>{item.detail}</div>
                    <div>{item.suggest}</div>
                    {index < reasonInfos.length - 1 && <div style={{ height: '8px' }} />}
                  </div>
                ))}
              </div>
            ),
          };
        }
      } catch (error) {
        console.error('解析shopScoreInspectInfo失败:', error);
        // 解析失败时继续使用原有逻辑
      }
    }

    // 场景1：商家分质检通过 + 商品货架质检通过
    if (merchantScore.passed && productShelf.passed) {
      return null; // 无弹窗，直接提报成功
    }

    // 场景2：商家分质检通过 + 商品货架质检不通过
    if (merchantScore.passed && !productShelf.passed && productShelf.hasResult) {
      return {
        title: '提示',
        content: (
          <div>
            <div>
              抱歉，您当前提报质检结果很可能为<span style={{ color: '#ff4d4f' }}>不通过</span>
              <div style={{ height: '8px' }} />
            </div>
            <div>原因：团购货架质检不通过</div>
            <div>明细：{productShelf.reason || '暂无详细信息'}</div>
            <div>建议：请基于原因明细，修改团购货架</div>
          </div>
        ),
      };
    }

    // 场景3：商家分质检不通过 + 商品货架质检通过
    if (!merchantScore.passed && productShelf.passed) {
      let merchantReason: React.ReactNode = '';
      let merchantSuggestion: React.ReactNode = '';

      // 根据版本设置不同的文案
      if (merchantScore.version >= '3.0') {
        merchantReason = (
          <>
            当前门店等级为LV {merchantScore.level || 2}，
            <span style={{ color: '#ff4d4f' }}>低于</span>
            标准LV3
          </>
        );
        merchantSuggestion = '请提升门店LV级别，至少不低于LV3';
      } else {
        // 1.0版本或其他版本都按1.0处理
        merchantReason = (
          <>
            当前门店商家分分值1.0为{merchantScore.score || 3}分，
            <span style={{ color: '#ff4d4f' }}>低于</span>标准4分
          </>
        );
        merchantSuggestion = (
          <>
            请完成更多商家分任务，提升商家分数值；若已完成商家分任务，请在完成后的
            <span style={{ color: '#ff4d4f' }}>1-3天再提报</span>
            ，以免因指标计算延迟，导致门店质检不过审
          </>
        );
      }

      return {
        title: '提示',
        content: (
          <div>
            <div>
              抱歉，您当前提报质检结果很可能为<span style={{ color: '#ff4d4f' }}>不通过</span>
              <div style={{ height: '8px' }} />
            </div>
            <div>原因：商家分质检不达标</div>
            <div>明细：{merchantReason}</div>
            <div>建议：{merchantSuggestion}</div>
          </div>
        ),
      };
    }

    // 场景4：商家分质检不通过 + 商品货架质检不通过
    if (!merchantScore.passed && !productShelf.passed && productShelf.hasResult) {
      let merchantReason: React.ReactNode = '';
      let merchantSuggestion: React.ReactNode = '';

      // 根据版本设置不同的文案
      if (merchantScore.version >= '3.0') {
        merchantReason = (
          <>
            当前门店等级为LV {merchantScore.level || 2}，
            <span style={{ color: '#ff4d4f' }}>低于</span>
            标准LV3
          </>
        );
        merchantSuggestion = '请提升门店LV级别，至少不低于LV3';
      } else {
        // 1.0版本或其他版本都按1.0处理
        merchantReason = (
          <>
            当前门店商家分分值1.0为{merchantScore.score || 3}分，
            <span style={{ color: '#ff4d4f' }}>低于</span>标准4分
          </>
        );
        merchantSuggestion = (
          <>
            请完成更多商家分任务，提升商家分数值；若已完成商家分任务，请在完成后的
            <span style={{ color: '#ff4d4f' }}>1-3天再提报</span>
            ，以免因指标计算延迟，导致门店质检不过审
          </>
        );
      }

      return {
        title: '提示',
        content: (
          <div>
            <div>
              抱歉，您当前提报质检结果很可能为<span style={{ color: '#ff4d4f' }}>不通过</span>
            </div>
            <div>原因1：团购货架质检不达标</div>
            <div>明细：{productShelf.reason || '暂无详细信息'}</div>
            <div>建议：请基于原因明细，修改团购货架</div>
            <div style={{ height: '8px' }} />
            <div>原因2：商家分质检不达标</div>
            <div>明细：{merchantReason}</div>
            <div>建议：{merchantSuggestion}</div>
          </div>
        ),
      };
    }

    // 场景5：商家分质检通过 + 商品货架质检无结果
    // 根据文档：无弹窗，直接显示"提报成功，最晚T+2天内出审核结果"
    if (merchantScore.passed && !productShelf.hasResult) {
      return null; // 无弹窗，但需要特殊的成功消息
    }

    // 场景6：商家分质检不通过 + 商品货架质检无结果
    if (!merchantScore.passed && !productShelf.hasResult) {
      let merchantReason: React.ReactNode = '';
      let merchantSuggestion: React.ReactNode = '';

      // 根据版本设置不同的文案
      if (merchantScore.version >= '3.0') {
        merchantReason = (
          <>
            当前门店等级为LV {merchantScore.level || 2}，
            <span style={{ color: '#ff4d4f' }}>低于</span>
            标准LV3
          </>
        );
        merchantSuggestion = '请提升门店LV级别，至少不低于LV3';
      } else {
        // 1.0版本或其他版本都按1.0处理
        merchantReason = (
          <>
            当前门店商家分分值1.0为{merchantScore.score || 3}分，
            <span style={{ color: '#ff4d4f' }}>低于</span>标准4分
          </>
        );
        merchantSuggestion = (
          <>
            请完成更多商家分任务，提升商家分数值；若已完成商家分任务，请在完成后的
            <span style={{ color: '#ff4d4f' }}>1-3天再提报</span>
            ，以免因指标计算延迟，导致门店质检不过审
          </>
        );
      }

      return {
        title: '提示',
        content: (
          <div>
            <div>
              抱歉，您当前提报质检结果很可能为<span style={{ color: '#ff4d4f' }}>不通过</span>
              <div style={{ height: '8px' }} />
            </div>
            <div>原因：商家分质检不达标</div>
            <div>明细：{merchantReason}</div>
            <div>建议：{merchantSuggestion}</div>
          </div>
        ),
      };
    }

    return null;
  };

  // 获取质检结果
  const getQualityCheckResult = async (): Promise<QualityCheckResult> => {
    try {
      // 调用查询接口获取门店质检相关信息
      const res = await queryLocalOptEspWoosOrder({ shopId });

      // 根据接口文档，返回结构应该有data字段，但类型定义可能不准确
      const auditData = (res as any)?.data || res;
      const { shopScoreInfo, shopItemInspectInfo, orderAuditType, shopScoreInspectInfo } =
        auditData;

      console.log('接口返回数据:', res);
      console.log('shopScoreInfo:', shopScoreInfo);
      console.log('shopItemInspectInfo:', shopItemInspectInfo);
      console.log('shopScoreInspectInfo:', shopScoreInspectInfo);

      // 处理商家分质检结果
      const merchantScore = {
        passed: shopScoreInfo?.outstandingLevel === true,
        version: shopScoreInfo?.scoreVersion || '1.0',
        score: shopScoreInfo?.score, // 1.0版本使用score字段
        level:
          shopScoreInfo?.scoreVersion && shopScoreInfo?.scoreVersion >= '3.0'
            ? shopScoreInfo?.grade // 3.0及以上版本使用grade字段，
            : undefined,
      };

      // 如果关键数据为undefined，则认为商家分质检不通过
      if (
        merchantScore.version === '1.0' &&
        (merchantScore.score === undefined || merchantScore.score === null)
      ) {
        merchantScore.passed = false;
      }
      if (
        merchantScore.version >= '3.0' &&
        (merchantScore.level === undefined || merchantScore.level === null)
      ) {
        merchantScore.passed = false;
      }

      // 处理商品质检结果
      const productShelf = {
        passed: shopItemInspectInfo?.inspectResult === 'PASS',
        hasResult:
          shopItemInspectInfo?.inspectResult && shopItemInspectInfo?.inspectResult !== 'UNABLE',
        reason: shopItemInspectInfo?.failReason,
      };

      return {
        merchantScore,
        productShelf,
        orderAuditType,
        shopScoreInspectInfo,
      };
    } catch (error) {
      console.error('获取质检结果失败:', error);
      // 错误情况下返回默认值，不阻塞提报流程
      return {
        merchantScore: {
          passed: true,
          version: '1.0',
        },
        productShelf: {
          passed: true,
          hasResult: true,
        },
        orderAuditType: undefined, // 错误情况下不显示弹窗
      };
    }
  };

  // ESP工单提报处理逻辑
  const { runAsync: handleSubmit, loading: submitting } = useRequest(
    async () => {
      // 1. 获取质检结果
      const qualityCheckResult = await getQualityCheckResult();

      // 2. 根据质检结果显示对应弹窗
      const modalConfig = getQualityCheckModal(qualityCheckResult);
      // 只有当 modalConfig 存在且不是仅用于成功消息标记时才显示弹窗
      if (modalConfig && !modalConfig.isSuccessMessage) {
        await new Promise((resolve, reject) => {
          Modal.confirm({
            title: modalConfig.title,
            content: modalConfig.content,
            okText: '坚持提报',
            cancelText: '取消',
            onOk: () => {
              resolve(true);
            },
            onCancel: () => {
              reject(new Error('取消提报'));
            },
          });
        });
      }

      // 4. 运维2多次提报警告
      if (submitPermission.needWarning && submitPermission.warningMessage) {
        await new Promise((resolve, reject) => {
          Modal.confirm({
            title: '提示',
            content: submitPermission.warningMessage,
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
              resolve(true);
            },
            onCancel: () => {
              reject(new Error('取消提报'));
            },
          });
        });
      }

      // // 5. 质检结果不通过时的确认提示
      // if (isRejection) {
      //   await new Promise((resolve, reject) => {
      //     Modal.confirm({
      //       title: '提示',
      //       content: '请确认，原审核工单不申诉后，再次提报审核',
      //       okText: '确认',
      //       cancelText: '取消',
      //       onOk: () => {
      //         resolve(true);
      //       },
      //       onCancel: () => {
      //         reject(new Error('取消提报'));
      //       },
      //     });
      //   });
      // }

      // 6. 表单数据验证和处理
      let collectShopName = '';
      // 新增：审核通过时跳过目标门店名称校验
      if (props.auditStatus === 'AUDIT_PASS') {
        // 审核通过直接跳过校验
        collectShopName = form.getFieldValue('collectShopName')?.trim() || '';
      } else {
        const { collectShopName: name = '' } = form.getFieldsValue();
        const { hasNoMerchant: _hasNoMerchant } = form.getFieldsValue();
        collectShopName = name.trim();
        if (!_hasNoMerchant && !collectShopName) {
          message.error('请填写目标门店名称');
          return;
        }
        if (_hasNoMerchant) {
          collectShopName = '';
        }
      }

      // 7. 提交ESP工单
      const auditId = await createEspOrder({
        shopId,
        collectShopName,
      });

      // 8. 提报成功消息 - 根据质检结果显示不同消息
      // 场景5：商家分通过 + 商品货架无结果 = 显示特殊成功消息
      if (qualityCheckResult.merchantScore.passed && !qualityCheckResult.productShelf.hasResult) {
        message.success('提报成功，最晚T+2天内出审核结果');
      } else {
        message.success('提报成功');
      }

      // 9. 提报完成后自动收起卡片
      setExpanded(false);

      // 10. 刷新审核数据 - 添加短暂延迟处理完成
      setTimeout(() => {
        refreshAuditData(auditId);
      }, 1000);
    },
    {
      manual: true,
      debounceWait: 500,
      debounceLeading: true,
      debounceTrailing: false,
    },
  );

  return {
    handleSubmit,
    submitting,
  };
}
